cc.Class({
    extends: cc.Component,

    properties: {
        moveSpeed: 200,
        rotateSpeed: 360,
    },

    onLoad() {
       
        
        // 设置节点名称用于识别
        this.node.name = "coin";
    },

    start() {
        // 播放金币动画（如果有的话）
        let animation = this.getComponent(cc.Animation);
        if (animation) {
            animation.play();
        }
    },

    update(dt) {
        // 向左移动
        this.node.x -= this.moveSpeed * dt;
        
        // 旋转效果
        this.node.angle -= this.rotateSpeed * dt;
        
        // 当金币移出屏幕左侧时销毁
        if (this.node.x < -cc.winSize.width / 2 - 100) {
            this.node.destroy();
        }
    },
    
    onBeginContact(contact, selfCollider, otherCollider) {
        // 检查碰撞的是否是玩家
        if (otherCollider.node.name === "player") {
            console.log("金币被玩家收集");
            
            // 播放收集金币的音效（如果有的话）
            // cc.audioEngine.playEffect(this.collectSound, false);
            
            // 增加得分
            let gameManager = cc.find("Canvas").getComponent("GameManager");
            if (gameManager) {
                gameManager.addScore(10); // 增加10分
            }
            
            // 销毁金币
            this.node.destroy();
        }
    },
    
    // 确保在销毁节点时移除事件监听
    onDestroy() {
        this.node.off('onBeginContact', this.onBeginContact, this);
    }
});