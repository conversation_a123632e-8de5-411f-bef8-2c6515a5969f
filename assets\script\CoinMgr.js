
cc.Class({
    extends: cc.Component,

    properties: {
        coinprefab: cc.Prefab,

        // 金币生成间隔（秒）
        spawnInterval: {
            default: 2,
            tooltip: "金币生成间隔时间"
        }
    },

    // LIFE-CYCLE CALLBACKS:


    onLoad () {
          // 初始化金币移动速度
          this.moveSpeed = 200;

          // 开始生成金币
          this.spawnCoin();
          this.schedule(this.spawnCoin, this.spawnInterval);
          console.log("金币管理器初始化完成");
    },

    start () {

    },

    // update (dt) {},
    spawnCoin() {
        // 创建金币
        let coin = cc.instantiate(this.coinprefab);
        this.node.addChild(coin);

        // 设置金币位置（屏幕右侧随机高度）
        coin.x = cc.winSize.width / 2 + 100;

        // 改进Y轴位置：在地面以上的合理范围内生成
        // 假设地面在Y=0附近，金币生成在50-300的高度范围
        coin.y = 50 + Math.random() * 250; // 50到300的随机高度

        // 设置金币的移动速度
        let coinScript = coin.getComponent('Coin');
        if (coinScript) {
            coinScript.moveSpeed = this.moveSpeed;
        }

        console.log("生成金币: 位置(", coin.x, ",", coin.y, "), 移动速度:", this.moveSpeed);
    },

    // 设置移动速度，由GameManager调用
    setMoveSpeed(speed) {
        this.moveSpeed = speed;

        // 更新所有现有金币的移动速度
        this.node.children.forEach(child => {
            if (child.name === "coin") {
                let coinScript = child.getComponent('Coin');
                if (coinScript) {
                    coinScript.moveSpeed = speed;
                }
            }
        });
    },
});
