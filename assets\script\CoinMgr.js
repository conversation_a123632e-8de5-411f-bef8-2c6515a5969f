
cc.Class({
    extends: cc.Component,

    properties: {
        coinprefab: cc.Prefab,
    },

    // LIFE-CYCLE CALLBACKS:

    
    onLoad () {
          // 开始生成金币
          this.spawnCoin();
          this.schedule(this.spawnCoin, 1); // 每1秒生成一个金币
          console.log("金币管理器初始化完成");
    },

    start () {

    },

    // update (dt) {},
    spawnCoin() {

        
        // 创建金币
        let coin = cc.instantiate(this.coinprefab);
        this.node.addChild(coin);
        
        // 设置金币位置（屏幕右侧随机高度）
        coin.x = cc.winSize.width / 2 + 100;
        coin.y = Math.random() * 100 - 50; // 随机高度
        
        console.log("生成金币: 位置(", coin.x, ",", coin.y, ")");
    },
});
