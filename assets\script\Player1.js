cc.Class({
    extends: cc.Component,

    properties: {
        // 跳跃力度
        jumpForce: 600,
        
        // 最大下落高度，超过此高度视为游戏失败
        maxFallHeight: 500,
        
        // 最大跳跃次数（包括地面跳跃）
        maxJumpTimes: 3,
        
        // 玩家在X轴的固定位置
        fixedPositionX: -200,
        
        // // 音效
        // jumpSound: {
        //     default: null,
        //     type: cc.AudioClip
        // },
        
        // fallSound: {
        //     default: null,
        //     type: cc.AudioClip
        // }
    },

    onLoad() {
        console.log("player1脚本已加载");
        
        // 初始化状态
        this.isGameOver = false;
        this.jumpCount = 0;
        this.isOnGround = false;
        
        // 获取刚体组件
        this.rigidBody = this.getComponent(cc.RigidBody);
        if (!this.rigidBody) {
            console.error('Player节点缺少RigidBody组件!');
            return;
        }
        
        // 强制设置刚体为固定旋转
        this.rigidBody.fixedRotation = true;
        
        // 记录初始位置（用于重置）
        this.initialPosition = cc.v2(this.node.x, this.node.y);
        
        // 设置输入事件
        this.setupInputEvents();
        
        // 设置碰撞事件
        this.setupCollisionEvents();
    },

    start() {
        // 固定X轴位置
        this.node.x = this.fixedPositionX;
        console.log("player1脚本start");
    },

    update(dt) {
        // 保持X轴位置不变
        this.node.x = this.fixedPositionX;
        
        // 检查是否超过最大下落高度
        if (!this.isGameOver && this.initialPosition.y - this.node.y > this.maxFallHeight) {
            this.gameOver();
        }
        
        // 输出当前位置和速度(调试用)
        // console.log("Position:", this.node.x, this.node.y, "Velocity:", this.rigidBody.linearVelocity.y);
    },
    
    setupInputEvents() {
        // 设置键盘跳跃事件
        cc.systemEvent.on(cc.SystemEvent.EventType.KEY_DOWN, this.onKeyDown, this);
        
        // 设置触摸事件（移动设备）
        this.node.parent.on(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
        
        console.log("输入事件已设置");
    },
    
    onTouchStart(event) {
        console.log("触摸屏幕");
        this.jump();
    },
    
    setupCollisionEvents() {
        // 开启碰撞监听
        let collider = this.getComponent(cc.PhysicsBoxCollider);
        if (collider) {
            collider.sensor = false;
            
            // 确保启用了碰撞监听
            this.rigidBody.enabledContactListener = true;
            
            console.log("碰撞事件已设置");
        } else {
            console.error("Player节点缺少PhysicsBoxCollider组件!");
        }
    },
    
    onKeyDown(event) {
        console.log("按键按下:", event.keyCode);
        
        // 空格或上方向键跳跃
        if (event.keyCode === cc.macro.KEY.space || 
            event.keyCode === cc.macro.KEY.w ||
            event.keyCode === cc.macro.KEY.up) {
            this.jump();
        }
    },
    
    jump() {
        console.log("尝试跳跃, 当前在地面:", this.isOnGround, "跳跃次数:", this.jumpCount, "最大跳跃次数:", this.maxJumpTimes);

        // 如果游戏结束，不允许跳跃
        if (this.isGameOver) {
            console.log("游戏已结束，禁止跳跃");
            return;
        }

        // 跳跃条件：
        // 1. 在地面上时，总是可以跳跃（这是第一次跳跃）
        // 2. 在空中时，检查跳跃次数是否小于最大跳跃次数
        if (this.isOnGround || this.jumpCount < this.maxJumpTimes) {
            // 直接设置速度 (这种方式比施加脉冲更可靠)
            this.rigidBody.linearVelocity = cc.v2(0, this.jumpForce);

            // 增加跳跃计数
            this.jumpCount++;

            // 标记不在地面上
            this.isOnGround = false;

            console.log("跳跃成功! 当前跳跃次数:", this.jumpCount, "速度:", this.rigidBody.linearVelocity.y);

            // // 播放跳跃音效
            // if (this.jumpSound) {
            //     cc.audioEngine.playEffect(this.jumpSound, false);
            // }
        } else {
            console.log("无法跳跃: 已达到最大跳跃次数");
        }
    },
    
    onBeginContact(contact, selfCollider, otherCollider) {
        console.log("碰撞开始:", otherCollider.node.name);
        
        // 如果碰到地面
        if (otherCollider.node.name.startsWith('Ground')) {
            // 获取碰撞的法线向量
            let normal = contact.getWorldManifold().normal;
            
            // 如果法线向上，说明玩家落在地面上
            if (normal.y > 0.5) {
                console.log("玩家落地");
                this.isOnGround = true;
                this.jumpCount = 0; // 重置跳跃次数
            }
        }
    },
    
    onEndContact(contact, selfCollider, otherCollider) {
        console.log("碰撞结束:", otherCollider.node.name);
        
        // 如果离开地面
        if (otherCollider.node.name.startsWith('Ground')) {
            // 给玩家一个很短的缓冲时间再标记为离开地面
            // 这样可以避免因为地面间隙导致的问题
            this.scheduleOnce(() => {
                this.isOnGround = false;
            }, 0.1);
        }
    },
    
    gameOver() {
        this.isGameOver = true;
        
        // 播放失败音效
        if (this.fallSound) {
            cc.audioEngine.playEffect(this.fallSound, false);
        }
        
        console.log('玩家游戏结束！');
    },
    
    reset() {
        // 重置位置
        this.node.x = this.initialPosition.x;
        this.node.y = this.initialPosition.y;
        
        // 重置状态
        this.isGameOver = false;
        this.jumpCount = 0;
        this.isOnGround = false;
        
        // 重置速度
        if (this.rigidBody) {
            this.rigidBody.linearVelocity = cc.v2(0, 0);
        }
    },
    
    onDestroy() {
        // 移除事件监听
        cc.systemEvent.off(cc.SystemEvent.EventType.KEY_DOWN, this.onKeyDown, this);
        this.node.parent.off(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
    }
});
